"""
案例 4: 有记忆的智能体

目标: 学习如何为智能体添加存储功能，使其拥有跨对话的长期记忆。

我们将为“独眼杰克”船长配置一个“记忆宝箱”(SQLite数据库)，
让他能记住和你的每一次对话。

如何运行:
1. 确保已安装: pip install agno openai sqlalchemy typer
2. 设置你的OpenAI API密钥。
3. 运行脚本: python learning_agno/case_4_agent_with_memory.py
"""
import typer
from textwrap import dedent
from agno.agent import Agent
from agno.models.openai import OpenAIChat
# 1. 导入存储后端
from agno.storage.sqlite import SqliteStorage

# 2. 定义你的存储配置
# `SqliteStorage` 是一个简单的本地数据库存储方案。
# `db_file` 是数据库文件的路径，`table_name` 是存储聊天记录的表名。
captain_storage = SqliteStorage(db_file="tmp/pirate_memory.db", table_name="pirate_chat")
print("--- 船长的记忆宝箱已备好！ ---")

# 3. 创建一个拥有记忆的智能体
# 我们使用 `typer` 来创建一个简单的命令行应用，以便进行多轮对话。
def chat_with_captain(user: str = "default_user"):
    agent = Agent(
        model=OpenAIChat(id="gpt-4o"),
        instructions=dedent("""\
            你是一位脾气古怪但经验丰富的海盗船长，“独眼杰克”。
            你有一个“记忆宝箱”，能帮你记住之前的所有对话。

            你的风格指南:
            - 你要能记起之前跟用户聊过的话题。
            - 如果用户重复问问题，你要能指出来。
            - 总是以“听明白了么，你这旱鸭子！”结尾。
        """),
        # `storage`: 将我们配置好的存储实例传递给智能体。
        storage=captain_storage,
        # `user_id`: 为当前用户指定一个ID，这样记忆就不会混淆。
        user_id=user,
        # `read_chat_history=True`: 关键！这会给智能体一个内置工具，
        # 让它在回应前，能自动读取与当前用户的聊天记录。
        read_chat_history=True,
        show_tool_calls=True,
        markdown=True,
    )

    print(f"--- 开始与船长对话 (用户: {user}, 会话ID: {agent.session_id}) ---")
    print("--- 输入 'exit' 来结束对话 ---")

    # `cli_app()` 会启动一个交互式命令行，让你能和智能体连续对话。
    agent.cli_app()

# 4. 运行命令行应用
if __name__ == "__main__":
    typer.run(chat_with_captain) 