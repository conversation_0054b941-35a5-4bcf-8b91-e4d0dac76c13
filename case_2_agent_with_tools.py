"""
案例 2: 会上网的智能体

目标: 学会为智能体添加工具，让它具备与外部世界交互的能力。

我们将为“独眼杰克”船长装上一个“望远镜”（网页搜索工具），
让他能查探七海之外的实时信息。

如何运行:
1. 确保你已经安装了必要的库: pip install agno openai duckduckgo-search
2. 设置你的OpenAI API密钥。
3. 运行这个脚本: python learning_agno/case_2_agent_with_tools.py
"""
from textwrap import dedent
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.tools.duckduckgo import DuckDuckGoTools  # 1. 导入你需要的工具

# 2. 创建一个带工具的智能体
# 和案例1相比，我们增加了 `tools` 和 `show_tool_calls` 两个参数。
pirate_captain_agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    instructions=dedent("""\
        你是一位脾气古怪但经验丰富的海盗船长，名叫“独眼杰克”。
        你的任务是以海盗的口吻和智慧来回答所有问题。

        你的风格指南:
        - 如果问题超出了你的航海知识，就毫不犹豫地使用你的“魔法望远镜”（搜索工具）来查探一番。
        - 查探到信息后，用你自己的海盗黑话转述出来。
        - 总是以“听明白了么，你这旱鸭子！”结尾。
    """),
    # `tools`: 接收一个工具实例的列表。这里我们给了他 DuckDuckGo 搜索工具。
    tools=[DuckDuckGoTools()],
    # `show_tool_calls=True`: 这非常重要！它会在运行时打印出智能体
    # 的“思考过程”，让你能看到它是否决定使用工具，以及用了哪个工具。
    show_tool_calls=True,
    markdown=True,
)

# 3. 提出一个需要上网查询的问题
# 这个问题超出了船长自身的知识范围，看他是否会使用工具。
print("--- 船长，打听个事儿！ ---")
pirate_captain_agent.print_response(
    "船长，帮我查查今天黄金的价格是多少？俺想知道俺的宝藏值多少钱！", stream=True
) 