"""
案例 5: 智能体团队协作

目标: 学习 Agno 最强大的功能之一——智能体团队（Agent Teams）。
我们将创建一个“研究与报告”团队，来解决一个需要多步骤、多技能的复杂任务。

团队构成:
- 首席研究员: 一个只负责用工具上网搜索原始信息的专家。
- 金牌撰稿人: 一个只负责将原始信息整合成报告的写作专家。
- 团队协调员 (由Team自动管理): 负责理解任务、分配工作、流转信息。

如何运行:
1. 确保已安装: pip install agno openai duckduckgo-search
2. 设置你的OpenAI API密钥。
3. 运行脚本: python learning_agno/case_5_agent_team.py
"""
from textwrap import dedent
from agno.agent import Agent
from agno.team import Team
from agno.models.openai import OpenAIChat
from agno.tools.duckduckgo import DuckDuckGoTools

# 1. 定义团队成员 - 各司其职的专家

# 专家1: 首席研究员
# - 他只有一个工具：DuckDuckGo搜索。
# - 他的任务很纯粹：只管搜索，把原始资料找出来。
researcher_agent = Agent(
    name="首席研究员",
    role="你是一个专业的网络研究员。",
    instructions=[
        "你的任务是根据给定的主题，使用搜索工具在互联网上查找最相关、最权威的信息。",
        "你不需要对信息进行总结或分析，只需要将原始的、未经处理的资料链接和摘要提供出来。",
    ],
    model=OpenAIChat(id="gpt-4o"),
    tools=[DuckDuckGoTools()],
)

# 专家2: 金牌撰稿人
# - 他没有任何工具。
# - 他的任务也很纯粹：根据研究员给他的资料，写出一篇好文章。
writer_agent = Agent(
    name="金牌撰稿人",
    role="你是一位才华横溢的科技专栏作家。",
    instructions=[
        "你的任务是根据提供给你的原始研究资料，撰写一篇内容详实、逻辑清晰、文笔流畅的报告。",
        "你不能自己搜索信息，所有信息都必须来源于给你的资料。",
        "报告需要有引言、主体和结论。",
    ],
    model=OpenAIChat(id="gpt-4o"),
)

# 2. 组建团队
# - `Team` 类用于将多个智能体组合在一起。
# - `members`: 把我们刚才定义的专家们放进去。
# - `mode='coordinate'`: 这是协调模式，意味着团队协调员会像项目经理一样，
#   根据指令来决定任务的执行顺序（先研究 -> 再写作）。
# - `instructions`: 这是给团队“协调员”的指令，告诉他如何调度团队成员。
# - `success_criteria`: 告诉协调员什么时候任务才算圆满完成。
research_team = Team(
    name="研究与报告团队",
    members=[researcher_agent, writer_agent],
    mode="coordinate",
    model=OpenAIChat(id="gpt-4o"),
    instructions=[
        "你的目标是制作一份关于特定主题的深度研究报告。",
        "工作流程非常明确：",
        "1. 首先，你必须派遣【首席研究员】去网上搜集所有相关的原始信息。",
        "2. 然后，将【首席研究员】搜集到的所有资料，原封不动地交给【金牌撰稿人】。",
        "3. 最后，让【金牌撰稿人】根据这些资料撰写最终的报告。",
        "确保最终的输出就是这份报告本身，不要有多余的客套话。",
    ],
    success_criteria="一份内容详实、结构清晰、信息来源可靠的研究报告已经完成。",
    show_tool_calls=True,
    show_members_responses=True,  # 让我们能看到每个成员的输出
    markdown=True,
)

# 3. 给团队分配一个复杂任务
print("--- 团队任务启动 ---")
research_team.print_response(
    "请深入研究一下“大语言模型（LLMs）的最新发展趋势”，并给我一份详细的报告。",
    stream=True,
) 