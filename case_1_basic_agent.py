"""
案例 1: 你的第一个智能体

目标: 学习如何使用 Agno 创建一个最基础的、具有特定个性的AI智能体。

在这个例子中，我们将创建一个“海盗船长”智能体。

如何运行:
1. 确保你已经安装了必要的库: pip install agno openai
2. 在终端中设置你的OpenAI API密钥:
   - Mac/Linux: export OPENAI_API_KEY='你的密钥'
   - Windows: set OPENAI_API_KEY='你的密钥'
3. 运行这个脚本: python learning_agno/case_1_basic_agent.py
"""
from textwrap import dedent
from agno.agent import Agent
from agno.models.openai import OpenAIChat

# 1. 创建你的第一个智能体
# 我们通过实例化 Agent 类来创建一个智能体。
# - `model`: 指定智能体使用的大语言模型。这里我们用 gpt-4o。
# - `instructions`: 这是给智能体的核心指令，也就是它的“系统提示”。
#   我们在这里定义了它的角色、说话风格和任务。
# - `markdown=True`: 让输出的格式更好看。
pirate_captain_agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    instructions=dedent("""\
        你是一位脾气古怪但经验丰富的海盗船长，名叫“独眼杰克”。
        你的任务是以海盗的口吻和智慧来回答所有问题。

        你的风格指南:
        - 多用“俺”、“你小子”、“宝藏”、“扬帆起航”等地道的海盗黑话。
        - 你的回答应该简短、直接，充满自信。
        - 对任何现代科技都表示出困惑和轻蔑。
        - 总是以“听明白了么，你这旱鸭子！”结尾。
    """),
    markdown=True,
)

# 2. 与智能体对话
# `print_response` 是运行智能体并打印结果的最简单方法。
# - `stream=True`: 开启流式输出，让回答像打字一样逐字显示。
print("--- 船长，俺有个问题！ ---")
pirate_captain_agent.print_response(
    "船长，能告诉我什么是'人工智能'吗？", stream=True
) 