# 欢迎来到 Agno 框架新手训练营！

你好，未来的智能体大师！

这个文件夹是为你量身打造的 Agno 框架入门教程。我将通过四个循序渐进的案例，手把手带你掌握构建 AI 智能体的核心技巧。

我们将以创建一个充满乐趣的“独眼杰克”海盗船长智能体为主线，逐步为他解锁新的能力。

---

### 准备工作

在开始之前，请确保你已经安装了所有案例可能需要的 Python 库。你可以在终端中运行以下命令一次性安装它们：

```bash
pip install agno openai duckduckgo-search lancedb tantivy sqlalchemy typer rich
```

同时，请确保你已经设置了你的 OpenAI API 密钥。在你的终端中运行：

-   **Mac / Linux:**
    ```bash
    export OPENAI_API_KEY='sk-...'
    ```
-   **Windows (CMD):**
    ```bash
    set OPENAI_API_KEY=sk-...
    ```

---

### 学习路径

#### 案例 1: 你的第一个智能体 (`case_1_basic_agent.py`)

-   **学习目标:** 掌握创建最基础智能体的方法，并学习如何通过 `instructions` 参数赋予它独特的个性和行为模式。
-   **核心代码:** `Agent`, `OpenAIChat`
-   **如何运行:**
    ```bash
    python learning_agno/case_1_basic_agent.py
    ```
-   **观察要点:** 查看船长是否完全按照你的指令，用海盗的口吻进行了回答。

---

#### 案例 2: 会上网的智能体 (`case_2_agent_with_tools.py`)

-   **学习目标:** 学会使用 `tools` 参数为智能体集成外部工具（如此处的网页搜索），让它能获取实时信息。
-   **核心代码:** `tools=[DuckDuckGoTools()]`, `show_tool_calls=True`
-   **如何运行:**
    ```bash
    python learning_agno/case_2_agent_with_tools.py
    ```
-   **观察要点:** 注意终端中打印出的 `[Tool Call]` 信息。这表明船长意识到自己的知识不足，并决定使用“魔法望远镜”（搜索工具）来寻找答案。

---

#### 案例 3: 有专属知识的智能体 (`case_3_agent_with_knowledge.py`)

-   **学习目标:** 学习如何通过 `knowledge` 参数，为智能体挂载一个本地知识库（RAG），让它成为特定领域的专家。
-   **核心代码:** `knowledge=TextKnowledgeBase(...)`, `vector_db=LanceDb(...)`, `embedder=OpenAIEmbedder(...)`
-   **如何运行:**
    ```bash
    python learning_agno/case_3_agent_with_knowledge.py
    ```
-   **观察要点:**
    1.  第一次运行时，会看到知识库被加载和处理。
    2.  船长能够准确回答关于“Agno框架”的问题，而这些信息只存在于我们提供的 `my_knowledge.txt` 文件中。
    3.  注意观察 `[Tool Call]`，你会看到它调用了一个名为 `knowledge_base_search` 的内部工具。

---

#### 案例 4: 有记忆的智能体 (`case_4_agent_with_memory.py`)

-   **学习目标:** 学习如何使用 `storage` 参数为智能体提供长期记忆，使其能够跨会话记住对话历史。
-   **核心代码:** `storage=SqliteStorage(...)`, `user_id`, `read_chat_history=True`
-   **如何运行:**
    ```bash
    python learning_agno/case_4_agent_with_memory.py
    ```
-   **观察要点:**
    1.  这是一个交互式应用。第一次和它对话时，问它一个问题，比如“我最喜欢的水果是香蕉”。
    2.  结束对话（输入`exit`），然后重新运行程序。
    3.  再次启动对话后，问它：“我最喜欢的水果是什么？”
    4.  如果它能回答“香蕉”，就证明它的记忆生效了！观察 `[Tool Call]`，你会看到它调用了 `read_chat_history` 工具。

---

现在，扬帆起航，开始你的 Agno 探险之旅吧！祝你好运，未来的船长！ 