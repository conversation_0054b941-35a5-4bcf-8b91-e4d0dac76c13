"""
案例 3: 有专属知识的智能体

目标: 学会为智能体装载一个本地知识库，让它在特定领域成为专家。

我们将让“独眼杰克”船长读取他的秘密航海日志(`my_knowledge.txt`)，
这样他就能回答关于“代码之海”的专业问题了。

如何运行:
1. 确保你已安装: pip install agno openai lancedb tantivy
2. 设置你的OpenAI API密钥。
3. 运行脚本: python learning_agno/case_3_agent_with_knowledge.py
"""
from textwrap import dedent
from agno.agent import Agent
from agno.models.openai import OpenAIChat
# 1. 导入知识库、向量数据库和嵌入模型相关的类
from agno.knowledge.text import TextKnowledgeBase
from agno.vectordb.lancedb import LanceDb
from agno.embedder.openai import OpenAIEmbedder

# 2. 定义你的知识库
# 这是一个RAG（检索增强生成）的核心配置。
# - `TextKnowledgeBase`: 指定我们要从一个本地文本文件中加载知识。
#   `path` 指向我们的知识文件。
# - `vector_db`: 知识需要被转换成向量并存储起来，才能被快速检索。
#   `LanceDb` 是一个简单好用的本地向量数据库。
#   `uri` 是数据库文件的存放位置，`table_name` 是数据表名。
# - `embedder`: 指定一个将文本转换成向量的嵌入模型。
captain_knowledge = TextKnowledgeBase(
    path="learning_agno/my_knowledge.txt",
    vector_db=LanceDb(
        uri="tmp/lancedb_pirate",
        table_name="pirate_log",
        embedder=OpenAIEmbedder(id="text-embedding-3-small"),
    ),
)

# 3. 加载知识库
# `load()` 方法会读取文件、生成向量、并存入数据库。
# 如果数据库已存在，它会自动跳过，不会重复加载。
captain_knowledge.load()
print("--- 船长的秘密航海日志已加载！ ---")

# 4. 创建一个拥有知识库的智能体
pirate_captain_agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    instructions=dedent("""\
        你是一位脾气古怪但经验丰富的海盗船长，“独眼杰克”。
        你现在拥有了你那本从不离身的秘密航海日志作为知识。

        你的风格指南:
        - 当被问及“代码之海”或“Agno框架”时，你必须首先查阅你的航海日志（知识库）。
        - 优先使用日志里的信息来回答，因为那是你最权威的知识。
        - 总是以“听明白了么，你这旱鸭子！”结尾。
    """),
    # `knowledge` 参数: 将我们配置好的知识库实例传递给智能体。
    knowledge=captain_knowledge,
    show_tool_calls=True,
    markdown=True,
)

# 5. 提出一个与知识库相关的问题
# 这个问题只能通过查阅航海日志来回答。
print("--- 船长，俺想知道个秘密... ---")
pirate_captain_agent.print_response(
    "船长，跟我讲讲'Agno框架'是个啥宝贝？", stream=True
) 